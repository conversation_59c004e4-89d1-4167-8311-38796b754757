import type { RoomStatusConfig, AreaConfig, QuickAction, RecentOpening, Room } from "../types/room";

// 房间状态配置
export const ROOM_STATUS_CONFIG: RoomStatusConfig[] = [
  { key: "occupied", label: "占用", color: "orange", count: 0 },
  { key: "free", label: "可用", color: "green", count: 0 },
  { key: "cleaning", label: "待清", color: "red", count: 0 },
  { key: "booked", label: "预定", color: "blue", count: 0 },
  { key: "maintenance", label: "维修", color: "purple", count: 0 },
  { key: "disabled", label: "停用", color: "gray", count: 0 },
  { key: "checkout", label: "结账", color: "yellow", count: 0 },
  { key: "transfer", label: "换房", color: "indigo", count: 0 },
  { key: "overtime", label: "超时", color: "red", count: 0 },
  { key: "vip-service", label: "VIP服务", color: "pink", count: 0 },
  { key: "event", label: "活动中", color: "cyan", count: 0 },
  { key: "reserved", label: "保留", color: "slate", count: 0 }
];

// 区域配置
export const AREA_CONFIG: AreaConfig[] = [
  { key: "all", label: "全部" },
  { key: "V", label: "VIP区" },
  { key: "M", label: "中包区" },
  { key: "S", label: "小包区" },
  { key: "X", label: "大包区" },
  { key: "P", label: "Party厅" },
  { key: "G", label: "花园VIP" }
];

// 区域详细配置（用于显示）
export const AREA_DISPLAY_CONFIG: Record<string, AreaConfig> = {
  V: {
    key: "V",
    label: "VIP区",
    icon: "crown",
    headerClass: "bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-100",
    textColor: "text-purple-600",
    dotColor: "bg-gradient-to-r from-purple-500 to-pink-500",
    tagType: "danger"
  },
  M: {
    key: "M",
    label: "中包区",
    icon: "users",
    headerClass: "bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-100",
    textColor: "text-blue-600",
    dotColor: "bg-gradient-to-r from-blue-500 to-cyan-500",
    tagType: "primary"
  },
  S: {
    key: "S",
    label: "小包区",
    icon: "user-circle",
    headerClass: "bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100",
    textColor: "text-green-600",
    dotColor: "bg-gradient-to-r from-green-500 to-emerald-500",
    tagType: "success"
  },
  X: {
    key: "X",
    label: "大包区",
    icon: "star",
    headerClass: "bg-gradient-to-r from-orange-50 to-red-50 border border-orange-100",
    textColor: "text-orange-600",
    dotColor: "bg-gradient-to-r from-orange-500 to-red-500",
    tagType: "warning"
  },
  P: {
    key: "P",
    label: "Party厅",
    icon: "music",
    headerClass: "bg-gradient-to-r from-pink-50 to-rose-50 border border-pink-100",
    textColor: "text-pink-600",
    dotColor: "bg-gradient-to-r from-pink-500 to-rose-500",
    tagType: "danger"
  },
  G: {
    key: "G",
    label: "花园VIP",
    icon: "leaf",
    headerClass: "bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-100",
    textColor: "text-emerald-600",
    dotColor: "bg-gradient-to-r from-emerald-500 to-teal-500",
    tagType: "success"
  }
};

// 快捷操作配置
export const QUICK_ACTIONS: QuickAction[] = [
  { key: "booking", label: "预订", icon: "plus" },
  { key: "member", label: "会员", icon: "users" },
  { key: "report", label: "报表", icon: "chart-line" }
];

// 最近开房记录（模拟数据）
export const RECENT_OPENINGS: RecentOpening[] = [
  { roomId: "P01", roomType: "Party厅", time: "15:12" },
  { roomId: "G02", roomType: "花园VIP", time: "14:58" },
  { roomId: "V01", roomType: "VIP套房", time: "14:20" },
  { roomId: "X05", roomType: "大包", time: "13:45" },
  { roomId: "M08", roomType: "中包", time: "13:11" }
];

// 模拟房间数据 - 60间房
export const MOCK_ROOMS: Room[] = [
  // VIP区 (V01-V08) - 8间
  {
    id: "V01",
    type: "VIP套房",
    status: "occupied",
    area: "V",
    startTime: "15:12",
    customer: "张先生",
    duration: "2小时30分",
    total: 580,
    items: [
      { id: "1", name: "包房费", quantity: 1, price: 200, total: 200 },
      { id: "2", name: "啤酒", quantity: 6, price: 30, total: 180 },
      { id: "3", name: "果盘", quantity: 2, price: 100, total: 200 }
    ]
  },
  { id: "V02", type: "VIP套房", status: "free", area: "V" },
  { id: "V03", type: "VIP套房", status: "booked", area: "V" },
  { id: "V04", type: "VIP套房", status: "cleaning", area: "V" },
  { id: "V05", type: "VIP套房", status: "occupied", area: "V" },
  { id: "V06", type: "VIP套房", status: "vip-service", area: "V" },
  { id: "V07", type: "VIP套房", status: "free", area: "V" },
  { id: "V08", type: "VIP套房", status: "checkout", area: "V" },

  // 中包区 (M01-M15) - 15间
  { id: "M01", type: "中包", status: "free", area: "M" },
  { id: "M02", type: "中包", status: "occupied", area: "M" },
  { id: "M03", type: "中包", status: "free", area: "M" },
  { id: "M04", type: "中包", status: "booked", area: "M" },
  { id: "M05", type: "中包", status: "free", area: "M" },
  { id: "M06", type: "中包", status: "cleaning", area: "M" },
  { id: "M07", type: "中包", status: "free", area: "M" },
  { id: "M08", type: "中包", status: "occupied", area: "M" },
  { id: "M09", type: "中包", status: "free", area: "M" },
  { id: "M10", type: "中包", status: "transfer", area: "M" },
  { id: "M11", type: "中包", status: "free", area: "M" },
  { id: "M12", type: "中包", status: "free", area: "M" },
  { id: "M13", type: "中包", status: "occupied", area: "M" },
  { id: "M14", type: "中包", status: "free", area: "M" },
  { id: "M15", type: "中包", status: "maintenance", area: "M" },

  // 小包区 (S01-S18) - 18间
  { id: "S01", type: "小包", status: "free", area: "S" },
  { id: "S02", type: "小包", status: "occupied", area: "S" },
  { id: "S03", type: "小包", status: "free", area: "S" },
  { id: "S04", type: "小包", status: "free", area: "S" },
  { id: "S05", type: "小包", status: "cleaning", area: "S" },
  { id: "S06", type: "小包", status: "free", area: "S" },
  { id: "S07", type: "小包", status: "occupied", area: "S" },
  { id: "S08", type: "小包", status: "free", area: "S" },
  { id: "S09", type: "小包", status: "booked", area: "S" },
  { id: "S10", type: "小包", status: "free", area: "S" },
  { id: "S11", type: "小包", status: "free", area: "S" },
  { id: "S12", type: "小包", status: "occupied", area: "S" },
  { id: "S13", type: "小包", status: "free", area: "S" },
  { id: "S14", type: "小包", status: "overtime", area: "S" },
  { id: "S15", type: "小包", status: "free", area: "S" },
  { id: "S16", type: "小包", status: "free", area: "S" },
  { id: "S17", type: "小包", status: "cleaning", area: "S" },
  { id: "S18", type: "小包", status: "free", area: "S" },

  // 大包区 (X01-X10) - 10间
  { id: "X01", type: "大包", status: "free", area: "X" },
  { id: "X02", type: "大包", status: "occupied", area: "X" },
  { id: "X03", type: "大包", status: "disabled", area: "X" },
  { id: "X04", type: "大包", status: "free", area: "X" },
  { id: "X05", type: "大包", status: "event", area: "X" },
  { id: "X06", type: "大包", status: "free", area: "X" },
  { id: "X07", type: "大包", status: "booked", area: "X" },
  { id: "X08", type: "大包", status: "free", area: "X" },
  { id: "X09", type: "大包", status: "cleaning", area: "X" },
  { id: "X10", type: "大包", status: "free", area: "X" },
