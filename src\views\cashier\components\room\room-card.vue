<template>
  <div
    :class="[
      'backdrop-blur-sm rounded-lg p-3 border-2 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105',
      getRoomStatusClass(props.room.status),
      getSelectedClass()
    ]"
    @click="selectRoom(props.room)"
  >
    <!-- 房间信息 -->
    <div class="text-center space-y-1.5">
      <!-- 房间号 -->
      <h4 class="text-lg font-bold text-slate-800">{{ props.room.id }}</h4>

      <!-- 房型 -->
      <div class="text-xs text-slate-600">{{ props.room.type }}</div>

      <!-- 状态标签 -->
      <div class="flex justify-center">
        <span
          :class="[
            'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
            getStatusTagClass(props.room.status)
          ]"
        >
          {{ getStatusText(props.room.status) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Room, RoomStatus } from "../../types/room";
import { getRoomStatusClass, getStatusText } from "../../utils/room-helpers";

defineOptions({
  name: "RoomCard"
});

// Props
interface Props {
  room: Room;
  selectedRoom: Room | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  selectRoom: [room: Room];
  roomAction: [action: string, room: Room];
}>();

// 获取状态标签样式
const getStatusTagClass = (status: RoomStatus): string => {
  const tagClasses: Record<RoomStatus, string> = {
    occupied: "bg-orange-100 text-orange-800",
    free: "bg-green-100 text-green-800",
    cleaning: "bg-red-100 text-red-800",
    booked: "bg-blue-100 text-blue-800",
    maintenance: "bg-purple-100 text-purple-800",
    disabled: "bg-gray-100 text-gray-800"
  };
  return tagClasses[status] || tagClasses.free;
};

// 获取选中样式（方案A：增强边框 + 背景高亮）
const getSelectedClass = (): string => {
  const isSelected = props.selectedRoom?.id === props.room.id;
  if (!isSelected) return "bg-white/90";

  return [
    "bg-blue-50/95", // 蓝色背景高亮
    "border-blue-500", // 蓝色边框
    "ring-4", // 更粗的环形边框
    "ring-blue-200/50", // 半透明蓝色环
    "shadow-xl", // 增强阴影
    "shadow-blue-200/30", // 蓝色阴影
    "scale-105", // 轻微放大
    "z-10", // 提升层级
    "relative" // 相对定位
  ].join(" ");
};

// 选择房间
const selectRoom = (room: Room) => {
  emit("selectRoom", room);
};
</script>
