<template>
  <div class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">收银确认弹窗演示</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <!-- 房间信息 -->
          <div class="bg-blue-50 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">房间信息</h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">房间号：</span>
                <span class="font-medium">205</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">房型：</span>
                <span class="font-medium">小房</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">状态：</span>
                <span class="font-medium text-orange-600">使用中</span>
              </div>
            </div>
          </div>

          <!-- 当前消费 -->
          <div class="bg-green-50 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-green-800 mb-3">当前消费</h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">房费：</span>
                <span class="font-medium">¥50</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">商品消费：</span>
                <span class="font-medium">¥0</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">总计：</span>
                <span class="font-bold text-green-600">¥50</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-4">
          <button
            @click="openProductModal"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <font-awesome-icon icon="shopping-cart" class="mr-2" />
            商品落单
          </button>
          
          <button
            @click="openDirectCheckout"
            class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
          >
            <font-awesome-icon icon="cash-register" class="mr-2" />
            直接收银
          </button>
        </div>

        <!-- 说明 -->
        <div class="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h4 class="text-yellow-800 font-medium mb-2">功能说明：</h4>
          <ul class="text-sm text-yellow-700 space-y-1">
            <li>• 点击"商品落单"可以选择商品并进入收银确认流程</li>
            <li>• 点击"直接收银"可以直接进入收银确认（仅房费）</li>
            <li>• 收银确认弹窗包含完整的订单信息、费用汇总和支付方式选择</li>
            <li>• 支持现金、微信、支付宝、银行卡、会员卡、挂账等多种支付方式</li>
            <li>• 自动计算找零，支持备注信息</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 商品选择弹窗 -->
    <product-selection-modal
      v-if="showProductModal"
      :room-info="roomInfo"
      @close="closeProductModal"
      @confirm="handleOrderConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import ProductSelectionModal from './components/order/product-selection-modal.vue';
import type { OrderForm } from './types/product';

defineOptions({
  name: 'DemoCheckout'
});

// 响应式数据
const showProductModal = ref(false);

const roomInfo = ref({
  id: '205',
  type: '小房'
});

// 方法
const openProductModal = () => {
  showProductModal.value = true;
};

const closeProductModal = () => {
  showProductModal.value = false;
};

const openDirectCheckout = () => {
  // 这里可以直接打开收银确认弹窗
  // 暂时用消息提示代替
  ElMessage.info('直接收银功能演示：这里会直接打开收银确认弹窗');
};

const handleOrderConfirm = (orderForm: OrderForm) => {
  console.log('订单确认:', orderForm);
  ElMessage.success(`订单确认成功！房间：${orderForm.roomId}，商品数量：${orderForm.items.length}`);
  closeProductModal();
};
</script>

<style scoped>
/* 演示页面样式 */
.demo-page {
  font-family: "Inter", "Noto Sans SC", sans-serif;
}
</style>
