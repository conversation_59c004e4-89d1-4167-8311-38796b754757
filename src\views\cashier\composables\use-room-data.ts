import { ref, reactive, computed } from "vue";
import type { Room, RoomStatusConfig } from "../types/room";
import { MOCK_ROOMS, ROOM_STATUS_CONFIG } from "../constants/room-config";
import { calculateStatusCounts, filterRooms } from "../utils/room-helpers";

export function useRoomData() {
  // 响应式数据
  const allRooms = reactive<Room[]>([...MOCK_ROOMS]);
  const roomStatuses = reactive<RoomStatusConfig[]>([...ROOM_STATUS_CONFIG]);
  const searchKeyword = ref("");
  const currentArea = ref("all");
  const selectedRoom = ref<Room | null>(null);

  // 计算属性 - 过滤后的房间
  const filteredRooms = computed(() => {
    return filterRooms(allRooms, searchKeyword.value, currentArea.value);
  });

  // 更新状态统计
  const updateStatusCounts = () => {
    const counts = calculateStatusCounts(allRooms);
    roomStatuses.forEach(status => {
      status.count = counts[status.key];
    });
  };

  // 根据房间ID查找房间
  const findRoomById = (roomId: string): Room | undefined => {
    return allRooms.find(room => room.id === roomId);
  };

  // 更新房间状态
  const updateRoomStatus = (roomId: string, newStatus: Room["status"]) => {
    const room = findRoomById(roomId);
    if (room) {
      room.status = newStatus;
      updateStatusCounts();
    }
  };

  // 选择房间
  const selectRoom = (room: Room) => {
    if (selectedRoom.value && selectedRoom.value.id === room.id) {
      selectedRoom.value = null; // 如果再次点击同一个房间，则取消选择
    } else {
      selectedRoom.value = room; // 否则，选择新房间
    }
  };

  // 清除选择
  const clearSelection = () => {
    selectedRoom.value = null;
  };

  // 初始化
  updateStatusCounts();

  return {
    // 数据
    allRooms,
    roomStatuses,
    searchKeyword,
    currentArea,
    selectedRoom,
    
    // 计算属性
    filteredRooms,
    
    // 方法
    updateStatusCounts,
    findRoomById,
    updateRoomStatus,
    selectRoom,
    clearSelection
  };
}
